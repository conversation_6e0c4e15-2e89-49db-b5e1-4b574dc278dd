logging:
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint}|%clr(%5p)|%clr(${PID:- }){magenta}|%clr(%15.15t){faint}|%clr(${spring.application.name}){cyan}|%clr(%X{X-B3-TraceId:-N/A}){blue}|%X{X-B3-SpanId:-N/A}|%clr(%-40.40logger{39}){cyan}|%m%n%wEx"
    file: ${logging.pattern.console}
  file:
    name: logs/${spring.application.name}.log
    max-history: 14

spring:
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true
      routes:
        - id: xingyun-cloud-api
          uri: lb://xingyun-cloud-api
          predicates:
            - Path=/cloud-api/**
          filters:
            - StripPrefix=1
        - id: xingyun-dynamic-api
          uri: lb://xingyun-cloud-api
          predicates:
            - Path=/dynamic/web/**,/dynamic-api/**
        - id: xingyun-oss
          uri: lb://xingyun-cloud-api
          predicates:
            - Path=/oss/**