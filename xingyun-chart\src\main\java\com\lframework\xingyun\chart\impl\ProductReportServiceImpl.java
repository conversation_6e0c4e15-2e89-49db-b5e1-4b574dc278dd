package com.lframework.xingyun.chart.impl;

import com.lframework.starter.common.utils.DateUtil;
import com.lframework.starter.web.core.utils.ApplicationUtil;
import com.lframework.starter.web.core.components.security.SecurityUtil;
import com.lframework.starter.web.core.components.security.AbstractUserDetails;
import com.lframework.xingyun.basedata.entity.ProductCategory;
import com.lframework.xingyun.basedata.service.product.ProductCategoryService;
import com.lframework.xingyun.chart.mappers.ProductReportMapper;
import com.lframework.xingyun.chart.service.ProductReportService;
import com.lframework.xingyun.chart.vo.product.report.QueryProductReportVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class ProductReportServiceImpl implements ProductReportService {

    @Autowired
    private ProductReportMapper productReportMapper;

    @Override
    public List<Map<String, Object>> dailyCategoryAmountReport(QueryProductReportVo vo) {
        LocalDate startDate = LocalDate.parse(vo.getStartDate());
        LocalDate endDate = LocalDate.parse(vo.getEndDate());
        
        // 获取当前登录用户ID和角色信息
        AbstractUserDetails currentUser = SecurityUtil.getCurrentUser();
        String createById = currentUser.getId();
        boolean isAdmin = this.checkIsAdmin(currentUser);

        // 获取商品分类数据
        ProductCategoryService productCategoryService = ApplicationUtil.getBean(ProductCategoryService.class);
        List<ProductCategory> categories = productCategoryService.getAllProductCategories();

        // 查询数据
        List<Map<String, Object>> rawData = isAdmin ?
                productReportMapper.dailyCategoryAmountReportAdmin(
                        DateUtil.toLocalDateTime(startDate), DateUtil.toLocalDateTimeMax(endDate)) :
                productReportMapper.dailyCategoryAmountReport(
                        DateUtil.toLocalDateTime(startDate), DateUtil.toLocalDateTimeMax(endDate), createById);

        // 按日期和分类分组
        Map<String, Map<String, BigDecimal>> dateCategoryMap = new HashMap<>();
        Set<String> datesWithData = new HashSet<>();
        for (Map<String, Object> item : rawData) {
            String date = formatDate(item.get("date"));
            String categoryId = (String) item.get("categoryId");
            BigDecimal amount = (BigDecimal) item.get("amount");

            dateCategoryMap.computeIfAbsent(date, k -> new HashMap<>())
                    .put(categoryId, amount);
            datesWithData.add(date);
        }

        // 构建结果 - 只返回有数据的日期
        List<Map<String, Object>> result = new ArrayList<>();
        for (String dateStr : datesWithData) {
            Map<String, Object> row = new HashMap<>();
            row.put("date", dateStr);

            // 添加每个分类的金额
            Map<String, BigDecimal> categoryAmounts = dateCategoryMap.getOrDefault(dateStr, new HashMap<>());
            for (ProductCategory category : categories) {
                row.put(category.getName(), categoryAmounts.getOrDefault(category.getId(), BigDecimal.ZERO));
            }

            result.add(row);
        }

        // 按日期排序
        result.sort((a, b) -> ((String) a.get("date")).compareTo((String) b.get("date")));

        return result;
    }

    @Override
    public List<Map<String, Object>> dailyInOutReport(QueryProductReportVo vo) {
        LocalDate startDate = LocalDate.parse(vo.getStartDate());
        LocalDate endDate = LocalDate.parse(vo.getEndDate());
        
        // 获取当前登录用户ID和角色信息
        AbstractUserDetails currentUser = SecurityUtil.getCurrentUser();
        String createById = currentUser.getId();
        boolean isAdmin = this.checkIsAdmin(currentUser);

        // 查询数据
        List<Map<String, Object>> rawData = isAdmin ?
                productReportMapper.dailyInOutReportAdmin(
                        DateUtil.toLocalDateTime(startDate), DateUtil.toLocalDateTimeMax(endDate)) :
                productReportMapper.dailyInOutReport(
                        DateUtil.toLocalDateTime(startDate), DateUtil.toLocalDateTimeMax(endDate), createById);

        // 按日期和商品分组
        Map<String, Map<String, Map<String, Object>>> dateProductMap = new HashMap<>();
        for (Map<String, Object> item : rawData) {
            String date = formatDate(item.get("date"));
            String productId = (String) item.get("productId");

            dateProductMap.computeIfAbsent(date, k -> new HashMap<>())
                    .put(productId, item);
        }

        // 构建结果
        List<Map<String, Object>> result = new ArrayList<>();
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            String dateStr = currentDate.format(DateTimeFormatter.ISO_DATE);

            Map<String, Map<String, Object>> productData = dateProductMap.getOrDefault(dateStr, new HashMap<>());
            for (Map<String, Object> item : productData.values()) {
                Map<String, Object> row = new HashMap<>();
                row.put("date", dateStr);
                row.put("productId", item.get("productId"));
                row.put("productName", item.get("productName"));
                row.put("categoryId", item.get("categoryId"));
                row.put("categoryName", item.get("categoryName"));
                row.put("purchasePrice", item.get("purchasePrice"));
                row.put("inQuantity", item.getOrDefault("inQuantity", 0));
                row.put("outQuantity", item.getOrDefault("outQuantity", 0));
                result.add(row);
            }

            currentDate = currentDate.plusDays(1);
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> monthlyUnitPriceReport(QueryProductReportVo vo) {
        LocalDate startDate = LocalDate.parse(vo.getStartDate());
        LocalDate endDate = LocalDate.parse(vo.getEndDate());
        
        // 获取当前登录用户ID和角色信息
        AbstractUserDetails currentUser = SecurityUtil.getCurrentUser();
        String createById = currentUser.getId();
        boolean isAdmin = this.checkIsAdmin(currentUser);

        // 查询数据
        List<Map<String, Object>> rawData = isAdmin ?
                productReportMapper.monthlyUnitPriceReportAdmin(
                        DateUtil.toLocalDateTime(startDate), DateUtil.toLocalDateTimeMax(endDate)) :
                productReportMapper.monthlyUnitPriceReport(
                        DateUtil.toLocalDateTime(startDate), DateUtil.toLocalDateTimeMax(endDate), createById);

        // 构建结果
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> item : rawData) {
            Map<String, Object> row = new HashMap<>();
            row.put("date", formatDate(item.get("date")));
            row.put("productId", item.get("productId"));
            row.put("product", item.get("productName"));
            row.put("weight", item.getOrDefault("weight", BigDecimal.ZERO));
            row.put("totalAmount", item.getOrDefault("totalAmount", BigDecimal.ZERO));

            // 计算折合单价 = 总金额 / 重量
            BigDecimal weight = (BigDecimal) row.get("weight");
            BigDecimal totalAmount = (BigDecimal) row.get("totalAmount");
            if (weight != null && totalAmount != null && weight.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal unitPrice = totalAmount.divide(weight, 2, RoundingMode.HALF_UP);
                row.put("unitPrice", unitPrice);
            } else {
                row.put("unitPrice", BigDecimal.ZERO);
            }

            result.add(row);
        }

        return result;
    }
    
    /**
     * 判断当前用户是否为管理员
     * @param user 用户信息
     * @return 是否为管理员
     */
    private boolean checkIsAdmin(AbstractUserDetails user) {
        // 检查用户是否具有管理员权限（通过角色判断）
        // 这里简单判断用户名是否为admin，实际项目中应通过角色表关联查询
        return "admin".equals(user.getUsername());
    }
    
    /**
     * 格式化日期对象为字符串
     * @param dateObj 日期对象
     * @return 格式化后的日期字符串
     */
    private String formatDate(Object dateObj) {
        if (dateObj == null) {
            return null;
        }
        
        if (dateObj instanceof String) {
            return (String) dateObj;
        } else if (dateObj instanceof java.sql.Date) {
            return DateUtil.formatDate(((java.sql.Date) dateObj).toLocalDate());
        } else if (dateObj instanceof java.util.Date) {
            // 安全地转换 java.util.Date 到 java.sql.Date
            return DateUtil.formatDate(((java.util.Date) dateObj).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate());
        } else if (dateObj instanceof LocalDate) {
            return ((LocalDate) dateObj).format(DateTimeFormatter.ISO_DATE);
        } else if (dateObj instanceof java.time.LocalDateTime) {
            // 支持 java.time.LocalDateTime 类型
            return ((java.time.LocalDateTime) dateObj).format(DateTimeFormatter.ISO_DATE_TIME);
        }
        
        return dateObj.toString();
    }
}