<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>xingyun</artifactId>
    <groupId>com.lframework</groupId>
    <version>1.0.0-SNAPSHOT</version>
    <relativePath>../../pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>xingyun-cloud-api</artifactId>
  <name>【${project.artifactId}】SpringCloud架构的Api接口层</name>

  <dependencies>
    <dependency>
      <groupId>com.lframework</groupId>
      <artifactId>xingyun-comp</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lframework</groupId>
      <artifactId>xingyun-basedata</artifactId>
    </dependency>

    <dependency>
      <groupId>com.lframework</groupId>
      <artifactId>xingyun-sc</artifactId>
    </dependency>

    <dependency>
      <groupId>com.lframework</groupId>
      <artifactId>xingyun-settle</artifactId>
    </dependency>

    <dependency>
      <groupId>com.lframework</groupId>
      <artifactId>xingyun-chart</artifactId>
    </dependency>

    <dependency>
      <groupId>com.lframework</groupId>
      <artifactId>cloud-starter</artifactId>
    </dependency>
  </dependencies>

  <build>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-resources-plugin</artifactId>
        <version>3.2.0</version>
        <executions>
          <execution>
            <id>copy-resources-to-target</id>
            <phase>package</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}</outputDirectory>
              <resources>
                <resource>
                  <directory>${project.basedir}</directory>
                  <filtering>true</filtering>
                  <includes>
                    <include>Dockerfile</include>
                  </includes>
                </resource>
                <resource>
                  <directory>${project.basedir}/src/main/resources</directory>
                  <filtering>true</filtering>
                  <includes>
                    <include>scripts/*</include>
                  </includes>
                </resource>
              </resources>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
        <includes>
          <include>bootstrap.yml</include>
        </includes>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>false</filtering>
        <includes>
          <include>**</include>
        </includes>
        <excludes>
          <exclude>scripts/*</exclude>
          <exclude>project.yaml</exclude>
        </excludes>
      </resource>
    </resources>
  </build>
</project>
