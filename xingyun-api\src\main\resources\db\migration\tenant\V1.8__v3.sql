ALTER TABLE `sw_file_box` ADD COLUMN `content_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'ContentType' AFTER `url`;
ALTER TABLE `sw_file_box` MODIFY COLUMN `url` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'Url' AFTER `name`;
ALTER TABLE `sw_file_box` ADD COLUMN `file_type` tinyint(3) NOT NULL COMMENT '文件类型' AFTER `content_type`;
ALTER TABLE `sw_file_box` ADD COLUMN `file_size` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件大小' AFTER `file_type`;
ALTER TABLE `sw_file_box` ADD COLUMN `file_path` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件路径' AFTER `file_size`;
ALTER TABLE `sw_file_box` ADD COLUMN `file_suffix` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件后缀' AFTER `file_path`;
ALTER TABLE `sw_file_box` DROP COLUMN `available`;
DELETE FROM `sys_menu` WHERE `id` = '0001';
DELETE FROM `sys_menu` WHERE `id` = '2001003';
DELETE FROM `sys_menu` WHERE `id` = '2001003001';
DELETE FROM `sys_menu` WHERE `id` = '2001003002';
DELETE FROM `sys_menu` WHERE `id` = '2001003003';
DELETE FROM `sys_menu` WHERE `id` = '2001003004';
DELETE FROM `sys_menu` WHERE `id` = '2001003005';
DELETE FROM `sys_menu` WHERE `id` = '2001006';
DELETE FROM `sys_menu` WHERE `id` = '2001006001';
DELETE FROM `sys_menu` WHERE `id` = '2001006002';
DELETE FROM `sys_menu` WHERE `id` = '2001006003';
DELETE FROM `sys_menu` WHERE `id` = '9000006';
DELETE FROM `sys_menu` WHERE `id` = '9000007';
DELETE FROM `sys_menu` WHERE `id` = '9001002';
UPDATE `sys_menu` SET `code` = '1000', `name` = 'System', `title` = '系统管理', `icon` = 'ant-design:setting-outlined', `component_type` = NULL, `component` = '', `request_param` = NULL, `parent_id` = NULL, `sys_module_id` = '2', `path` = '/system', `no_cache` = 0, `display` = 0, `hidden` = 0, `permission` = '', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-04 00:22:05', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2023-10-27 17:01:09' WHERE `id` = '1000';
UPDATE `sys_menu` SET `code` = '1000001', `name` = 'Menu', `title` = '菜单管理', `icon` = NULL, `component_type` = 0, `component` = '/system/menu/index', `request_param` = NULL, `parent_id` = '1000', `sys_module_id` = '2', `path` = '/menu', `no_cache` = 0, `display` = 1, `hidden` = 0, `permission` = 'system:menu:query', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-05-08 18:37:01', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2023-10-27 17:01:09' WHERE `id` = '1000001';
UPDATE `sys_menu` SET `code` = '1000001001', `name` = '', `title` = '新增菜单', `icon` = NULL, `component_type` = 0, `component` = '', `request_param` = NULL, `parent_id` = '1000001', `sys_module_id` = '2', `path` = '', `no_cache` = 0, `display` = 2, `hidden` = 0, `permission` = 'system:menu:add', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-05-12 22:50:27', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2023-10-27 17:01:09' WHERE `id` = '1000001001';
UPDATE `sys_menu` SET `code` = '1000001002', `name` = '', `title` = '修改菜单', `icon` = NULL, `component_type` = 0, `component` = '', `request_param` = NULL, `parent_id` = '1000001', `sys_module_id` = '2', `path` = '', `no_cache` = 0, `display` = 2, `hidden` = 0, `permission` = 'system:menu:modify', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-05-12 23:23:33', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2023-10-27 17:00:27' WHERE `id` = '1000001002';
UPDATE `sys_menu` SET `code` = '1000001003', `name` = '', `title` = '删除菜单', `icon` = NULL, `component_type` = 0, `component` = '', `request_param` = NULL, `parent_id` = '1000001', `sys_module_id` = '2', `path` = '', `no_cache` = 0, `display` = 2, `hidden` = 0, `permission` = 'system:menu:delete', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-05-12 23:24:36', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2023-10-27 17:00:27' WHERE `id` = '1000001003';
UPDATE `sys_menu` SET `code` = '1000002', `name` = 'Dept', `title` = '部门管理', `icon` = NULL, `component_type` = 0, `component` = '/system/dept/index', `request_param` = NULL, `parent_id` = '1000', `sys_module_id` = '2', `path` = '/dept', `no_cache` = 0, `display` = 1, `hidden` = 0, `permission` = 'system:dept:query', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-05 01:09:27', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2023-10-27 17:00:27' WHERE `id` = '1000002';
UPDATE `sys_menu` SET `code` = '1000002001', `name` = '', `title` = '新增部门', `icon` = NULL, `component_type` = 0, `component` = '', `request_param` = NULL, `parent_id` = '1000002', `sys_module_id` = '2', `path` = '', `no_cache` = 0, `display` = 2, `hidden` = 0, `permission` = 'system:dept:add', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-06-27 01:33:31', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2023-10-27 17:00:27' WHERE `id` = '1000002001';
UPDATE `sys_menu` SET `code` = '1000002002', `name` = '', `title` = '修改部门', `icon` = NULL, `component_type` = 0, `component` = '', `request_param` = NULL, `parent_id` = '1000002', `sys_module_id` = '2', `path` = '', `no_cache` = 0, `display` = 2, `hidden` = 0, `permission` = 'system:dept:modify', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-06-27 01:33:47', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2023-10-27 17:00:27' WHERE `id` = '1000002002';
UPDATE `sys_menu` SET `code` = '1000002003', `name` = '', `title` = '部门权限', `icon` = NULL, `component_type` = 0, `component` = '', `request_param` = NULL, `parent_id` = '1000002', `sys_module_id` = '2', `path` = '', `no_cache` = 0, `display` = 2, `hidden` = 0, `permission` = 'system:dept:permission', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-06-27 01:33:47', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2023-10-27 17:00:27' WHERE `id` = '1000002003';
UPDATE `sys_menu` SET `code` = '1000012', `name` = 'OpenDomain', `title` = '开放域', `icon` = NULL, `component_type` = 0, `component` = '/system/open-domain/index', `request_param` = NULL, `parent_id` = '1001', `sys_module_id` = '14', `path` = '/open-domain', `no_cache` = 0, `display` = 1, `hidden` = 0, `permission` = 'system:open-domain:config', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-05-08 18:37:01', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-12-09 17:54:42' WHERE `id` = '1000012';
UPDATE `sys_menu` SET `code` = '1001', `name` = 'Platform', `title` = '平台管理', `icon` = 'ant-design:global-outlined', `component_type` = NULL, `component` = '', `request_param` = NULL, `parent_id` = NULL, `sys_module_id` = '1', `path` = '/platform', `no_cache` = 0, `display` = 0, `hidden` = 0, `permission` = '', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-04 00:22:05', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-07-04 00:34:23' WHERE `id` = '1001';
UPDATE `sys_menu` SET `code` = '1001001', `name` = 'OnelineCode', `title` = '在线开发', `icon` = NULL, `component_type` = 0, `component` = '/iframes/index', `request_param` = NULL, `parent_id` = '1001', `sys_module_id` = '1', `path` = '/online-code?src=${magic-api.base-url}${magic-api.web}/index.html', `no_cache` = 0, `display` = 1, `hidden` = 0, `permission` = 'system:online-code:config', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-05-08 18:37:01', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-12-09 17:54:42' WHERE `id` = '1001001';
UPDATE `sys_menu` SET `code` = '2000', `name` = 'BaseData', `title` = '基础信息管理', `icon` = 'ant-design:container-outlined', `component_type` = NULL, `component` = '', `request_param` = NULL, `parent_id` = NULL, `sys_module_id` = '3', `path` = '/base-data', `no_cache` = 0, `display` = 0, `hidden` = 0, `permission` = '', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-05 01:21:35', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-07-05 01:21:39' WHERE `id` = '2000';
UPDATE `sys_menu` SET `code` = '2001', `name` = 'Product', `title` = '商品中心', `icon` = 'ant-design:appstore-outlined', `component_type` = NULL, `component` = '', `request_param` = NULL, `parent_id` = NULL, `sys_module_id` = '4', `path` = '/product', `no_cache` = 0, `display` = 0, `hidden` = 0, `permission` = '', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-05 01:21:35', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-07-05 01:21:39' WHERE `id` = '2001';
UPDATE `sys_menu` SET `code` = '2002', `name` = 'Purchase', `title` = '采购管理', `icon` = 'ant-design:money-collect-outlined', `component_type` = NULL, `component` = '', `request_param` = NULL, `parent_id` = NULL, `sys_module_id` = '5', `path` = '/purchase', `no_cache` = 0, `display` = 0, `hidden` = 0, `permission` = '', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-05 01:21:35', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-07-05 01:21:39' WHERE `id` = '2002';
UPDATE `sys_menu` SET `code` = '2003', `name` = 'Sale', `title` = '销售管理', `icon` = 'ant-design:rocket-outlined', `component_type` = NULL, `component` = '', `request_param` = NULL, `parent_id` = NULL, `sys_module_id` = '6', `path` = '/sale', `no_cache` = 0, `display` = 0, `hidden` = 0, `permission` = '', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-05 01:21:35', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-07-05 01:21:39' WHERE `id` = '2003';
UPDATE `sys_menu` SET `code` = '2004', `name` = 'Retail', `title` = '零售管理', `icon` = 'ant-design:flag-outlined', `component_type` = NULL, `component` = '', `request_param` = NULL, `parent_id` = NULL, `sys_module_id` = '7', `path` = '/retail', `no_cache` = 0, `display` = 0, `hidden` = 0, `permission` = '', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-05 01:21:35', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-07-05 01:21:39' WHERE `id` = '2004';
UPDATE `sys_menu` SET `code` = '3000', `name` = 'StockManage', `title` = '库存管理', `icon` = 'ant-design:hdd-outlined', `component_type` = NULL, `component` = '', `request_param` = NULL, `parent_id` = NULL, `sys_module_id` = '8', `path` = '/stock', `no_cache` = 0, `display` = 0, `hidden` = 0, `permission` = '', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-05 01:21:35', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-07-05 01:21:39' WHERE `id` = '3000';
UPDATE `sys_menu` SET `code` = '3000004', `name` = 'TakeStock', `title` = '库存盘点', `icon` = 'ant-design:monitor-outlined', `component_type` = NULL, `component` = '', `request_param` = NULL, `parent_id` = NULL, `sys_module_id` = '9', `path` = '/take', `no_cache` = 0, `display` = 0, `hidden` = 0, `permission` = '', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-05 01:21:35', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-07-05 01:21:39' WHERE `id` = '3000004';
UPDATE `sys_menu` SET `code` = '3000005', `name` = 'StockAdjust', `title` = '库存调整', `icon` = 'ant-design:thunderbolt-outlined', `component_type` = NULL, `component` = '', `request_param` = NULL, `parent_id` = NULL, `sys_module_id` = '10', `path` = '/take-adjust', `no_cache` = 0, `display` = 0, `hidden` = 0, `permission` = '', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-05 01:21:35', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-07-05 01:21:39' WHERE `id` = '3000005';
UPDATE `sys_menu` SET `code` = '4000', `name` = 'SettleManage', `title` = '结算管理', `icon` = 'ant-design:credit-card-outlined', `component_type` = NULL, `component` = '', `request_param` = NULL, `parent_id` = NULL, `sys_module_id` = '11', `path` = '/settle', `no_cache` = 0, `display` = 0, `hidden` = 0, `permission` = '', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-05 01:21:35', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-07-05 01:21:39' WHERE `id` = '4000';
UPDATE `sys_menu` SET `code` = '4000007', `name` = 'SupplierSettleManage', `title` = '供应商结算', `icon` = NULL, `component_type` = NULL, `component` = '', `request_param` = NULL, `parent_id` = '4000', `sys_module_id` = '11', `path` = '/supplier', `no_cache` = 0, `display` = 0, `hidden` = 0, `permission` = '', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-05 01:21:35', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-07-05 01:21:39' WHERE `id` = '4000007';
UPDATE `sys_menu` SET `code` = '4000008', `name` = 'CustomerSettleManage', `title` = '客户结算', `icon` = NULL, `component_type` = NULL, `component` = '', `request_param` = NULL, `parent_id` = '4000', `sys_module_id` = '11', `path` = '/customer', `no_cache` = 0, `display` = 0, `hidden` = 0, `permission` = '', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-05 01:21:35', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-07-05 01:21:39' WHERE `id` = '4000008';
UPDATE `sys_menu` SET `code` = '5000', `name` = 'Logistics', `title` = '物流管理', `icon` = 'ant-design:gift-outlined', `component_type` = NULL, `component` = '', `request_param` = NULL, `parent_id` = NULL, `sys_module_id` = '15', `path` = '/logistics', `no_cache` = 0, `display` = 0, `hidden` = 0, `permission` = '', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-04 00:22:05', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-07-04 00:34:23' WHERE `id` = '5000';
UPDATE `sys_menu` SET `code` = '9000', `name` = 'Development', `title` = '开发管理', `icon` = 'ant-design:tool-outlined', `component_type` = NULL, `component` = '', `request_param` = NULL, `parent_id` = NULL, `sys_module_id` = '12', `path` = '/development', `no_cache` = 0, `display` = 0, `hidden` = 0, `permission` = '', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-04 00:22:05', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-07-04 00:34:23' WHERE `id` = '9000';
UPDATE `sys_menu` SET `code` = '9001', `name` = 'SmartWork', `title` = '便捷办公', `icon` = 'ant-design:read-outlined', `component_type` = NULL, `component` = '', `request_param` = NULL, `parent_id` = NULL, `sys_module_id` = '13', `path` = '/smart-work', `no_cache` = 0, `display` = 0, `hidden` = 0, `permission` = '', `is_special` = 1, `available` = 1, `description` = '', `create_by` = '系统管理员', `create_by_id` = '1', `create_time` = '2021-07-04 00:22:05', `update_by` = '系统管理员', `update_by_id` = '1', `update_time` = '2021-07-04 00:34:23' WHERE `id` = '9001';
ALTER TABLE `op_logs`
    MODIFY COLUMN `log_type` int(11) NOT NULL COMMENT '类别' AFTER `name`;