logging:
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint}|%clr(%5p)|%clr(${PID:- }){magenta}|%clr(%15.15t){faint}|%clr(${spring.application.name}){cyan}|%clr(%X{X-B3-TraceId:-N/A}){blue}|%X{X-B3-SpanId:-N/A}|%clr(%-40.40logger{39}){cyan}|%m%n%wEx"
    file: ${logging.pattern.console}
  level:
    com.lframework: DEBUG
    io.lettuce.core.protocol: OFF
    org.dromara.warm.flow: DEBUG

spring:
  servlet:
    multipart:
      #临时文件路径
      location: D:\tmp\tmp-datas
  datasource:
    dynamic:
      # 设置默认数据源
      primary: master
      # 是否严格匹配数据源 未匹配到指定数据源时true抛异常,false使用默认数据源
      strict: true
      datasource:
        master:
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **************************************************************************************************************************************************************************************************************************************************************************
          username: gatherwit
          password: Wit@5685199@!!!
          druid:
            initial-size: 10
            min-idle: 10
            max-active: 200
            max-wait: 60000
            time-between-eviction-runs-millis: 300000
            validation-query: SELECT 'x'
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
            filters: stat
  #redis配置，这个redis用来缓存业务数据
  redis:
    #地址
    host: 127.0.0.1
    #端口，默认为6379
    port: 6379
    #数据库
    database: 0
    #密码
    password:
    #连接超时时间
    timeout: 10s
    lettuce:
      pool:
        #连接池中的最小空闲连接
        min-idle: 0
        #连接池中的最大空闲连接
        max-idle: 8
        #连接池的最大数据库连接数
        max-active: 8
        #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  #RabbitMQ配置
  rabbitmq:
#    addresses: 127.0.0.1:5672             # RabbitMQ集群地址列表，多个地址用逗号分隔
#    virtual-host: /
#    username: guest
#    password:  guest
    addresses: hw.wit.cn:5672             # RabbitMQ集群地址列表，多个地址用逗号分隔
    virtual-host: /
    username: admin
    password: 123456
    connection-timeout: 60000 # 连接超时时间（毫秒），默认为60000毫秒（60秒）
    ssl:
      enabled: false       # 是否启用SSL，默认为false
      algorithm: TLSv1.2   # SSL算法，默认为TLSv1.2
    publisher-confirm-type: SIMPLE # 发布确认机制
    publisher-returns: true  # 是否开启发布返回机制，默认为false
    listener:
      type: direct
      simple:
        acknowledge-mode: AUTO # 消费者确认模式，默认为auto
        auto-startup: false
        retry:
          enabled: false       # 是否启用重试机制，默认为false
      direct:
        acknowledge-mode: AUTO # 消费者确认模式，默认为auto
        retry:
          enabled: false       # 是否启用重试机制，默认为false

sa-token:
  # 配置 Sa-Token 单独使用的 Redis 连接，将业务和鉴权独立
  alone-redis:
    # Redis数据库索引（默认为0），最好与业务数据的redis隔离
    database: 1
    # Redis服务器地址
    host: ${spring.redis.host}
    # Redis服务器连接端口
    port: ${spring.redis.port}
    # Redis服务器连接密码（默认为空）
    password: ${spring.redis.password}
    # 连接超时时间
    timeout: ${spring.redis.timeout}

jugg:
  # 公开上传
  upload:
    #下载时的baseUrl，与domain的区别：会在url后面拼接/**作为静态资源的url，而domain是后端系统的具体域名，下载时的完整url例如：upload.domain + upload.url/xxx.jpg
    url: /oss
    #下载时的域名
    domain: http://localhost:8080
    #上传文件的路径
    location: D:\upload\upload-data
  # 安全上传
  security-upload:
    #下载时的域名，一般情况下和公开上传的域名一致
    domain: ${jugg.upload.domain}
    # 上传文件的路径
    location: ${jugg.upload.location}
