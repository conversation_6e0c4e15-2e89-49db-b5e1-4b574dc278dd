spring:
  #redis配置，这个redis用来缓存业务数据
  redis:
    #地址
    host: 127.0.0.1
    #端口，默认为6379
    port: 6379
    #数据库
    database: 0
    #密码
    password: password
    #连接超时时间
    timeout: 10s
    lettuce:
      pool:
        #连接池中的最小空闲连接
        min-idle: 0
        #连接池中的最大空闲连接
        max-idle: 8
        #连接池的最大数据库连接数
        max-active: 8
        #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

sa-token:
  # 配置 Sa-Token 单独使用的 Redis 连接，将业务和鉴权独立
  alone-redis:
    # Redis数据库索引（默认为0），最好与业务数据的redis隔离
    database: 1
    # Redis服务器地址
    host: ${spring.redis.host}
    # Redis服务器连接端口
    port: ${spring.redis.port}
    # Redis服务器连接密码（默认为空）
    password: ${spring.redis.password}
    # 连接超时时间
    timeout: ${spring.redis.timeout}