package com.lframework.xingyun.chart.mappers;

import com.lframework.starter.web.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 商品报表 Mapper
 */
public interface ProductReportMapper extends BaseMapper {

    /**
     * 按天和商品分类统计的金额报表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param createById 创建人ID
     * @return
     */
    List<Map<String, Object>> dailyCategoryAmountReport(@Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime,
                                                        @Param("createById") String createById);

    /**
     * 按天和商品分类统计的金额报表(管理员)
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    List<Map<String, Object>> dailyCategoryAmountReportAdmin(@Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 按天统计的商品出入库报表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param createById 创建人ID
     * @return
     */
    List<Map<String, Object>> dailyInOutReport(@Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime,
                                               @Param("createById") String createById);

    /**
     * 按天统计的商品出入库报表(管理员)
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    List<Map<String, Object>> dailyInOutReportAdmin(@Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 按月统计的商品折合单价报表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param createById 创建人ID
     * @return
     */
    List<Map<String, Object>> monthlyUnitPriceReport(@Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime,
                                                     @Param("createById") String createById);
    
    /**
     * 按月统计的商品折合单价报表(管理员)
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    List<Map<String, Object>> monthlyUnitPriceReportAdmin(@Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);
}