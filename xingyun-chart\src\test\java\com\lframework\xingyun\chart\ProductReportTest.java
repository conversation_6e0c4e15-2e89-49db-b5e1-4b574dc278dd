package com.lframework.xingyun.chart;

import com.lframework.xingyun.chart.service.ProductReportService;
import com.lframework.xingyun.chart.vo.product.report.QueryProductReportVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;

/**
 * 商品报表测试
 */
@SpringBootTest
public class ProductReportTest {

    @Autowired
    private ProductReportService productReportService;

    @Test
    public void testDailyCategoryAmountReport() {
        QueryProductReportVo vo = new QueryProductReportVo();
        vo.setStartDate("2024-01-01");
        vo.setEndDate("2024-01-31");
        
        List<Map<String, Object>> result = productReportService.dailyCategoryAmountReport(vo);
        System.out.println("按天和商品分类统计的金额报表结果数量: " + result.size());
        
        if (!result.isEmpty()) {
            System.out.println("第一条记录: " + result.get(0));
        }
    }

    @Test
    public void testDailyInOutReport() {
        QueryProductReportVo vo = new QueryProductReportVo();
        vo.setStartDate("2024-01-01");
        vo.setEndDate("2024-01-31");
        
        List<Map<String, Object>> result = productReportService.dailyInOutReport(vo);
        System.out.println("按天统计的商品出入库报表结果数量: " + result.size());
        
        if (!result.isEmpty()) {
            System.out.println("第一条记录: " + result.get(0));
            System.out.println("字段包含: " + result.get(0).keySet());
        }
    }

    @Test
    public void testMonthlyUnitPriceReport() {
        QueryProductReportVo vo = new QueryProductReportVo();
        vo.setStartDate("2024-01-01");
        vo.setEndDate("2024-12-31");
        
        List<Map<String, Object>> result = productReportService.monthlyUnitPriceReport(vo);
        System.out.println("按月统计的商品折合单价报表结果数量: " + result.size());
        
        if (!result.isEmpty()) {
            System.out.println("第一条记录: " + result.get(0));
        } else {
            System.out.println("没有数据，可能原因:");
            System.out.println("1. 商品的weight字段为0或null");
            System.out.println("2. 指定时间范围内没有交易记录");
            System.out.println("3. 交易记录的状态不是3（已审核）");
        }
    }
}
