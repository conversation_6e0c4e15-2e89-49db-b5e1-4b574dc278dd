<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>xingyun</artifactId>
    <groupId>com.lframework</groupId>
    <version>1.0.0-SNAPSHOT</version>
    <relativePath>../../pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>xingyun-cloud-gateway</artifactId>

  <name>【${project.artifactId}】GateWay网关</name>

  <dependencies>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-gateway</artifactId>
    </dependency>

    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
    </dependency>

    <dependency>
      <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
      <groupId>com.alibaba.cloud</groupId>
    </dependency>

    <dependency>
      <artifactId>nacos-client</artifactId>
      <groupId>com.alibaba.nacos</groupId>
    </dependency>

    <dependency>
      <artifactId>spring-cloud-starter-zipkin</artifactId>
      <groupId>org.springframework.cloud</groupId>
    </dependency>
  </dependencies>

  <properties>
    <maven.deploy.skip>true</maven.deploy.skip>
  </properties>

  <build>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-resources-plugin</artifactId>
        <version>3.2.0</version>
        <executions>
          <execution>
            <id>copy-resources-to-target</id>
            <phase>package</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}</outputDirectory>
              <resources>
                <resource>
                  <directory>${project.basedir}</directory>
                  <filtering>true</filtering>
                  <includes>
                    <include>Dockerfile</include>
                  </includes>
                </resource>
                <resource>
                  <directory>${project.basedir}/src/main/resources</directory>
                  <filtering>true</filtering>
                  <includes>
                    <include>scripts/*</include>
                  </includes>
                </resource>
              </resources>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
        <includes>
          <include>bootstrap.yml</include>
        </includes>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>false</filtering>
        <includes>
          <include>**</include>
        </includes>
        <excludes>
          <exclude>scripts/*</exclude>
          <exclude>project.yaml</exclude>
        </excludes>
      </resource>
    </resources>
  </build>
</project>