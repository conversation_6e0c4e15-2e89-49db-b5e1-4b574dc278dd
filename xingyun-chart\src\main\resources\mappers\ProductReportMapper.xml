<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lframework.xingyun.chart.mappers.ProductReportMapper">

    <!-- 按天和商品分类统计的金额报表 -->
    <select id="dailyCategoryAmountReport" resultType="map">
        SELECT
            dates.date,
            pc.id AS categoryId,
            pc.name AS categoryName,
            COALESCE(SUM(amounts.amount), 0) AS amount
        FROM (
            SELECT DISTINCT DATE(create_time) AS date
            FROM (
                SELECT create_time FROM tbl_purchase_order WHERE status = 3 AND create_time >= #{startTime} AND create_time &lt;= #{endTime} AND create_by_id = #{createById}
                UNION ALL
                SELECT create_time FROM tbl_sale_out_sheet WHERE status = 3 AND create_time >= #{startTime} AND create_time &lt;= #{endTime} AND create_by_id = #{createById}
                UNION ALL
                SELECT create_time FROM tbl_retail_out_sheet WHERE status = 3 AND create_time >= #{startTime} AND create_time &lt;= #{endTime} AND create_by_id = #{createById}
            ) AS all_dates
        ) AS dates
        CROSS JOIN base_data_product_category pc
        LEFT JOIN (
            SELECT 
                DATE(o.create_time) AS date,
                p.category_id,
                SUM(d.tax_price * d.order_num) AS amount
            FROM tbl_purchase_order o
            LEFT JOIN tbl_purchase_order_detail d ON o.id = d.order_id
            LEFT JOIN base_data_product p ON d.product_id = p.id
            WHERE o.status = 3 AND o.create_time >= #{startTime} AND o.create_time &lt;= #{endTime} AND o.create_by_id = #{createById}
            GROUP BY DATE(o.create_time), p.category_id
            
            UNION ALL
            
            SELECT 
                DATE(s.create_time) AS date,
                p.category_id,
                SUM(d.tax_price * d.order_num) AS amount
            FROM tbl_sale_out_sheet s
            LEFT JOIN tbl_sale_out_sheet_detail d ON s.id = d.sheet_id
            LEFT JOIN base_data_product p ON d.product_id = p.id
            WHERE s.status = 3 AND s.create_time >= #{startTime} AND s.create_time &lt;= #{endTime} AND s.create_by_id = #{createById}
            GROUP BY DATE(s.create_time), p.category_id
            
            UNION ALL
            
            SELECT 
                DATE(r.create_time) AS date,
                p.category_id,
                SUM(d.tax_price * d.order_num) AS amount
            FROM tbl_retail_out_sheet r
            LEFT JOIN tbl_retail_out_sheet_detail d ON r.id = d.sheet_id
            LEFT JOIN base_data_product p ON d.product_id = p.id
            WHERE r.status = 3 AND r.create_time >= #{startTime} AND r.create_time &lt;= #{endTime} AND r.create_by_id = #{createById}
            GROUP BY DATE(r.create_time), p.category_id
        ) AS amounts ON dates.date = amounts.date AND pc.id = amounts.category_id
        GROUP BY dates.date, pc.id, pc.name
        ORDER BY dates.date, pc.name
    </select>
    
    <!-- 按天和商品分类统计的金额报表(管理员) -->
    <select id="dailyCategoryAmountReportAdmin" resultType="map">
        SELECT
            dates.date,
            pc.id AS categoryId,
            pc.name AS categoryName,
            COALESCE(SUM(amounts.amount), 0) AS amount
        FROM (
            SELECT DISTINCT DATE(create_time) AS date
            FROM (
                SELECT create_time FROM tbl_purchase_order WHERE status = 3 AND create_time >= #{startTime} AND create_time &lt;= #{endTime}
                UNION ALL
                SELECT create_time FROM tbl_sale_out_sheet WHERE status = 3 AND create_time >= #{startTime} AND create_time &lt;= #{endTime}
                UNION ALL
                SELECT create_time FROM tbl_retail_out_sheet WHERE status = 3 AND create_time >= #{startTime} AND create_time &lt;= #{endTime}
            ) AS all_dates
        ) AS dates
        CROSS JOIN base_data_product_category pc
        LEFT JOIN (
            SELECT 
                DATE(o.create_time) AS date,
                p.category_id,
                SUM(d.tax_price * d.order_num) AS amount
            FROM tbl_purchase_order o
            LEFT JOIN tbl_purchase_order_detail d ON o.id = d.order_id
            LEFT JOIN base_data_product p ON d.product_id = p.id
            WHERE o.status = 3 AND o.create_time >= #{startTime} AND o.create_time &lt;= #{endTime}
            GROUP BY DATE(o.create_time), p.category_id
            
            UNION ALL
            
            SELECT 
                DATE(s.create_time) AS date,
                p.category_id,
                SUM(d.tax_price * d.order_num) AS amount
            FROM tbl_sale_out_sheet s
            LEFT JOIN tbl_sale_out_sheet_detail d ON s.id = d.sheet_id
            LEFT JOIN base_data_product p ON d.product_id = p.id
            WHERE s.status = 3 AND s.create_time >= #{startTime} AND s.create_time &lt;= #{endTime}
            GROUP BY DATE(s.create_time), p.category_id
            
            UNION ALL
            
            SELECT 
                DATE(r.create_time) AS date,
                p.category_id,
                SUM(d.tax_price * d.order_num) AS amount
            FROM tbl_retail_out_sheet r
            LEFT JOIN tbl_retail_out_sheet_detail d ON r.id = d.sheet_id
            LEFT JOIN base_data_product p ON d.product_id = p.id
            WHERE r.status = 3 AND r.create_time >= #{startTime} AND r.create_time &lt;= #{endTime}
            GROUP BY DATE(r.create_time), p.category_id
        ) AS amounts ON dates.date = amounts.date AND pc.id = amounts.category_id
        GROUP BY dates.date, pc.id, pc.name
        ORDER BY dates.date, pc.name
    </select>

    <!-- 按天统计的商品出入库报表 -->
    <select id="dailyInOutReport" resultType="map">
        SELECT
            dates.date,
            products.productId,
            products.productName,
            COALESCE(in_data.inQuantity, 0) AS inQuantity,
            COALESCE(out_data.outQuantity, 0) AS outQuantity
        FROM (
            SELECT DISTINCT DATE(create_time) AS date
            FROM (
                SELECT create_time FROM tbl_purchase_order WHERE status = 3 AND create_time >= #{startTime} AND create_time &lt;= #{endTime} AND create_by_id = #{createById}
                UNION ALL
                SELECT create_time FROM tbl_sale_out_sheet WHERE status = 3 AND create_time >= #{startTime} AND create_time &lt;= #{endTime} AND create_by_id = #{createById}
                UNION ALL
                SELECT create_time FROM tbl_retail_out_sheet WHERE status = 3 AND create_time >= #{startTime} AND create_time &lt;= #{endTime} AND create_by_id = #{createById}
            ) AS all_dates
        ) AS dates
        CROSS JOIN (
            SELECT DISTINCT p.id AS productId, p.name AS productName
            FROM base_data_product p
            WHERE EXISTS (
                SELECT 1 FROM tbl_purchase_order_detail d JOIN tbl_purchase_order o ON d.order_id = o.id WHERE o.status = 3 AND d.product_id = p.id AND o.create_time >= #{startTime} AND o.create_time &lt;= #{endTime} AND o.create_by_id = #{createById}
            ) OR EXISTS (
                SELECT 1 FROM tbl_sale_out_sheet_detail d JOIN tbl_sale_out_sheet s ON d.sheet_id = s.id WHERE s.status = 3 AND d.product_id = p.id AND s.create_time >= #{startTime} AND s.create_time &lt;= #{endTime} AND s.create_by_id = #{createById}
            ) OR EXISTS (
                SELECT 1 FROM tbl_retail_out_sheet_detail d JOIN tbl_retail_out_sheet r ON d.sheet_id = r.id WHERE r.status = 3 AND d.product_id = p.id AND r.create_time >= #{startTime} AND r.create_time &lt;= #{endTime} AND r.create_by_id = #{createById}
            )
        ) AS products
        LEFT JOIN (
            SELECT 
                DATE(o.create_time) AS date,
                d.product_id,
                SUM(d.order_num) AS inQuantity
            FROM tbl_purchase_order o
            LEFT JOIN tbl_purchase_order_detail d ON o.id = d.order_id
            WHERE o.status = 3 AND o.create_time >= #{startTime} AND o.create_time &lt;= #{endTime} AND o.create_by_id = #{createById}
            GROUP BY DATE(o.create_time), d.product_id
        ) AS in_data ON dates.date = in_data.date AND products.productId = in_data.product_id
        LEFT JOIN (
            SELECT 
                DATE(s.create_time) AS date,
                d.product_id,
                SUM(d.order_num) AS outQuantity
            FROM tbl_sale_out_sheet s
            LEFT JOIN tbl_sale_out_sheet_detail d ON s.id = d.sheet_id
            WHERE s.status = 3 AND s.create_time >= #{startTime} AND s.create_time &lt;= #{endTime} AND s.create_by_id = #{createById}
            GROUP BY DATE(s.create_time), d.product_id
            
            UNION ALL
            
            SELECT 
                DATE(r.create_time) AS date,
                d.product_id,
                SUM(d.order_num) AS outQuantity
            FROM tbl_retail_out_sheet r
            LEFT JOIN tbl_retail_out_sheet_detail d ON r.id = d.sheet_id
            WHERE r.status = 3 AND r.create_time >= #{startTime} AND r.create_time &lt;= #{endTime} AND r.create_by_id = #{createById}
            GROUP BY DATE(r.create_time), d.product_id
        ) AS out_data ON dates.date = out_data.date AND products.productId = out_data.product_id
        ORDER BY dates.date, products.productName
    </select>
    
    <!-- 按天统计的商品出入库报表(管理员) -->
    <select id="dailyInOutReportAdmin" resultType="map">
        SELECT
            dates.date,
            products.productId,
            products.productName,
            COALESCE(in_data.inQuantity, 0) AS inQuantity,
            COALESCE(out_data.outQuantity, 0) AS outQuantity
        FROM (
            SELECT DISTINCT DATE(create_time) AS date
            FROM (
                SELECT create_time FROM tbl_purchase_order WHERE status = 3 AND create_time >= #{startTime} AND create_time &lt;= #{endTime}
                UNION ALL
                SELECT create_time FROM tbl_sale_out_sheet WHERE status = 3 AND create_time >= #{startTime} AND create_time &lt;= #{endTime}
                UNION ALL
                SELECT create_time FROM tbl_retail_out_sheet WHERE status = 3 AND create_time >= #{startTime} AND create_time &lt;= #{endTime}
            ) AS all_dates
        ) AS dates
        CROSS JOIN (
            SELECT DISTINCT p.id AS productId, p.name AS productName
            FROM base_data_product p
            WHERE EXISTS (
                SELECT 1 FROM tbl_purchase_order_detail d JOIN tbl_purchase_order o ON d.order_id = o.id WHERE o.status = 3 AND d.product_id = p.id AND o.create_time >= #{startTime} AND o.create_time &lt;= #{endTime}
            ) OR EXISTS (
                SELECT 1 FROM tbl_sale_out_sheet_detail d JOIN tbl_sale_out_sheet s ON d.sheet_id = s.id WHERE s.status = 3 AND d.product_id = p.id AND s.create_time >= #{startTime} AND s.create_time &lt;= #{endTime}
            ) OR EXISTS (
                SELECT 1 FROM tbl_retail_out_sheet_detail d JOIN tbl_retail_out_sheet r ON d.sheet_id = r.id WHERE r.status = 3 AND d.product_id = p.id AND r.create_time >= #{startTime} AND r.create_time &lt;= #{endTime}
            )
        ) AS products
        LEFT JOIN (
            SELECT 
                DATE(o.create_time) AS date,
                d.product_id,
                SUM(d.order_num) AS inQuantity
            FROM tbl_purchase_order o
            LEFT JOIN tbl_purchase_order_detail d ON o.id = d.order_id
            WHERE o.status = 3 AND o.create_time >= #{startTime} AND o.create_time &lt;= #{endTime}
            GROUP BY DATE(o.create_time), d.product_id
        ) AS in_data ON dates.date = in_data.date AND products.productId = in_data.product_id
        LEFT JOIN (
            SELECT 
                DATE(s.create_time) AS date,
                d.product_id,
                SUM(d.order_num) AS outQuantity
            FROM tbl_sale_out_sheet s
            LEFT JOIN tbl_sale_out_sheet_detail d ON s.id = d.sheet_id
            WHERE s.status = 3 AND s.create_time >= #{startTime} AND s.create_time &lt;= #{endTime}
            GROUP BY DATE(s.create_time), d.product_id
            
            UNION ALL
            
            SELECT 
                DATE(r.create_time) AS date,
                d.product_id,
                SUM(d.order_num) AS outQuantity
            FROM tbl_retail_out_sheet r
            LEFT JOIN tbl_retail_out_sheet_detail d ON r.id = d.sheet_id
            WHERE r.status = 3 AND r.create_time >= #{startTime} AND r.create_time &lt;= #{endTime}
            GROUP BY DATE(r.create_time), d.product_id
        ) AS out_data ON dates.date = out_data.date AND products.productId = out_data.product_id
        ORDER BY dates.date, products.productName
    </select>

    <!-- 按月统计的商品折合单价报表 -->
    <select id="monthlyUnitPriceReport" resultType="map">
        SELECT
            DATE_FORMAT(t.create_time, '%Y-%m') AS date,
            t.product_id AS productId,
            p.name AS productName,
            SUM(p.weight * t.order_num) AS weight,
            SUM(t.tax_amount) AS totalAmount
        FROM (
            SELECT 
                o.create_time,
                d.product_id,
                d.order_num,
                (d.tax_price * d.order_num) AS tax_amount
            FROM tbl_purchase_order o
            LEFT JOIN tbl_purchase_order_detail d ON o.id = d.order_id
            WHERE o.status = 3 AND o.create_time >= #{startTime} AND o.create_time &lt;= #{endTime} AND o.create_by_id = #{createById}
            
            UNION ALL
            
            SELECT 
                s.create_time,
                d.product_id,
                d.order_num,
                (d.tax_price * d.order_num) AS tax_amount
            FROM tbl_sale_out_sheet s
            LEFT JOIN tbl_sale_out_sheet_detail d ON s.id = d.sheet_id
            WHERE s.status = 3 AND s.create_time >= #{startTime} AND s.create_time &lt;= #{endTime} AND s.create_by_id = #{createById}
            
            UNION ALL
            
            SELECT 
                r.create_time,
                d.product_id,
                d.order_num,
                (d.tax_price * d.order_num) AS tax_amount
            FROM tbl_retail_out_sheet r
            LEFT JOIN tbl_retail_out_sheet_detail d ON r.id = d.sheet_id
            WHERE r.status = 3 AND r.create_time >= #{startTime} AND r.create_time &lt;= #{endTime} AND r.create_by_id = #{createById}
        ) AS t
        LEFT JOIN base_data_product p ON t.product_id = p.id
        WHERE p.weight > 0
        GROUP BY DATE_FORMAT(t.create_time, '%Y-%m'), t.product_id, p.name
        ORDER BY date, p.name
    </select>
    
    <!-- 按月统计的商品折合单价报表(管理员) -->
    <select id="monthlyUnitPriceReportAdmin" resultType="map">
        SELECT
            DATE_FORMAT(t.create_time, '%Y-%m') AS date,
            t.product_id AS productId,
            p.name AS productName,
            SUM(p.weight * t.order_num) AS weight,
            SUM(t.tax_amount) AS totalAmount
        FROM (
            SELECT 
                o.create_time,
                d.product_id,
                d.order_num,
                (d.tax_price * d.order_num) AS tax_amount
            FROM tbl_purchase_order o
            LEFT JOIN tbl_purchase_order_detail d ON o.id = d.order_id
            WHERE o.status = 3 AND o.create_time >= #{startTime} AND o.create_time &lt;= #{endTime}
            
            UNION ALL
            
            SELECT 
                s.create_time,
                d.product_id,
                d.order_num,
                (d.tax_price * d.order_num) AS tax_amount
            FROM tbl_sale_out_sheet s
            LEFT JOIN tbl_sale_out_sheet_detail d ON s.id = d.sheet_id
            WHERE s.status = 3 AND s.create_time >= #{startTime} AND s.create_time &lt;= #{endTime}
            
            UNION ALL
            
            SELECT 
                r.create_time,
                d.product_id,
                d.order_num,
                (d.tax_price * d.order_num) AS tax_amount
            FROM tbl_retail_out_sheet r
            LEFT JOIN tbl_retail_out_sheet_detail d ON r.id = d.sheet_id
            WHERE r.status = 3 AND r.create_time >= #{startTime} AND r.create_time &lt;= #{endTime}
        ) AS t
        LEFT JOIN base_data_product p ON t.product_id = p.id
        WHERE p.weight > 0
        GROUP BY DATE_FORMAT(t.create_time, '%Y-%m'), t.product_id, p.name
        ORDER BY date, p.name
    </select>
</mapper>