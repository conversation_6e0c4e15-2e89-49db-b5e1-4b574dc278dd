server:
  #端口
  port: 8280
spring:
  application:
    name: @project.artifactId@
  profiles:
    active: @profiles-active@
  servlet:
    multipart:
      #是否开启文件上传
      enabled: true
      #单个文件最大值
      max-file-size: 10MB
      #请求中所有文件的最大值
      max-request-size: 100MB
      #将文件写入磁盘的阈值
      #file-size-threshold: 0
      #是否要延迟解析文件
      #resolve-lazily: false
  quartz:
    jdbc:
      initialize-schema: never
    job-store-type: jdbc
    wait-for-jobs-to-complete-on-shutdown: true
  #时间格式
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: non_null
  #使用redis存储SESSION
  session:
    store-type: redis
  redis:
    lettuce:
      reader-idle-time-seconds: 30

knife4j:
  enable: true
  setting:
    language: zh-CN
    # 是否显示界面中SwaggerModel功能
    enableSwaggerModels: false
    # 是否显示界面中"文档管理"功能
    enableDocumentManage: true
    # 是否默认显示底部Footer
    enableFooter: false
    # 是否自定义Footer
    enableFooterCustom: true
    # 自定义Footer内容(支持Markdown语法)
    footerCustomContent: Apache License 2.0 | Copyright  2019-[星云ERP](https://gitee.com/lframework/xingyun)
    # 是否开启调试
    enableDebug: true
    # 是否显示OpenAPI原始结构的Tab框，默认显示
    enableOpenApi: false
  # 是否生产环境
  production: false

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)，此值不要修改
  token-name: X-Auth-Token
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 25920000
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: 7200
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false

jugg:
  secret:
    # 秘钥，用于敏感信息加密，生产环境一定要更改此项，并且产生数据后不要更改秘钥
    # 提供了EncryptUtil工具类，包含：生成秘钥、加密、解密方法。
    key: eRE2drUFINojkG9ivqtfOA==
  #默认配置
  default-setting:
    #默认的用户ID，用于写入数据库时的createById和updateById的自动赋值，如果有登录人则取登录人ID，如果没有则取默认用户ID，如果不配置，默认是1
    default-user-id: 1
    #默认的用户姓名，用于写入数据库时的createBy和updateBy的自动赋值，如果有登录人则取登录人姓名，如果没有则取默认用户姓名，如果不配置，默认是系统管理员
    default-user-name: 系统管理员
  web:
    #不需要认证的Url
    filter-url: ${magic-api.push_path}
    #终端ID
    worker-id: 1
    #数据中心ID
    center-id: 1
    #重复提交拦截暂时不实现
    #repeat-request:
      #重复提交拦截间隔（单位：秒）
      #filter-interval: 1
  #缓存TTL配置 单位：秒
  cache:
    #公共缓存过期时间
    ttl: 1800
    #特殊指定缓存过期时间，以下示例为cacheName的过期时间是3600秒、cacheName2的过期是7200秒。cacheName、cacheName2对应的是@Cacheable的Value属性
    #regions:
    #  cacheName: 3600
    #  cacheName2: 7200
    regions:
      product_stock_warning_notify: 86400
  #开启多租户
  tenant:
    enabled: false
  #开启websocket
  ws:
    enabled: true
    #通信topic
    #topic: xingyun:ws:topic
    #是否支持跨域
    #support-cross-domain: false
  #验证码配置
  kaptcha:
    #过期时间（单位：分钟）
    expire-time: 5
    session-config-key: KAPTCHA_SESSION_KEY
    session-config-date: KAPTCHA_SESSION_DATE
    border: true
    border-color: black
    border-thickness: 1
    noise-color: black
    noise-impl: com.google.code.kaptcha.impl.DefaultNoise
    obscurificator-impl: com.google.code.kaptcha.impl.WaterRipple
    producer-impl: com.google.code.kaptcha.impl.DefaultKaptcha
    text-producer-impl: com.google.code.kaptcha.text.impl.DefaultTextCreator
    text-producer-char-string: abcde2345678gfynmnpwx
    text-producer-char-length: 4
    text-producer-font-names: Arial,Courier
    text-producer-font-color: black
    text-producer-font-size: 28
    text-producer-char-space: 2
    word-render-impl: com.google.code.kaptcha.text.impl.DefaultWordRenderer
    back-ground-impl: com.google.code.kaptcha.impl.DefaultBackground
    back-ground-clr-from: lightGray
    back-ground-clr-to: white
    image-width: 120
    image-height: 40


#PageHelper分页器
pagehelper:
  offset-as-page-num: true
  row-bounds-with-count: true
  page-size-zero: true
  reasonable: false
  params: pageNum=pageIndex;pageSize=pageSize;
  support-methods-arguments: false
mybatis-plus:
  mapper-locations: classpath*:/mappers/**/*.xml
  typeEnumsPackage: com.lframework.starter.**.enums,com.lframework.xingyun.**.enums

op-logs:
  #是否开启
  enabled: true
  #操作日志保留天数
  retain-days: 7

# 是否移除内置权限，针对admin等内置权限，因为内置权限等同于通配符权限，设置为true时会用具体的权限将内置权限替换掉，生产环境建议设置为true
remove-fixed-permissions: false

magic-api:
  # 编辑器配置项
  editor-config: classpath:magic-editor-config.js
  # 这里改为项目运行时的域名，菜单中嵌入的页面为base-url + web，这里示例即为http://localhost:8080/dynamic/web
  base-url: http://localhost:8080
  # 编辑器页面的访问路径url
  web: /dynamic/web
  resource:
    type: database
    tableName: magic_api_file
    prefix: /magic-api
    readonly: false
  # 接口前缀
  prefix: /dynamic-api
  # 禁止覆盖应用接口
  allow-override: true
  # 是否支持跨域
  support-cross-domain: false
  # 推送路径
  push_path: /_magic-api-sync
  # 推送秘钥，自行修改
  secret-key: 123456789
  throw-exception: true
  response: |- #配置JSON格式，格式为magic-script中的表达式
    {
      code: code,
      msg: message,
      data,
      timestamp,
      requestTime,
      executeTime,
    }
  response-code:
    success: 200
    invalid: 400
    exception: 500
  page:
    size: pageSize # 页大小的参数名称
    page: pageIndex # 页码的参数名称
    default-page: 1 # 未传页码时的默认首页
    default-size: 20 # 未传页大小时的默认页大小
  backup:
    enable: true
    max-history: 14 #备份保留天数，-1为永久保留
    table-name: magic_api_file_backup

# warm-flow
warm-flow:
  token-name: ${sa-token.token-name}
  data-fill-handler-path: com.lframework.starter.bpm.handlers.BpmDataFillHandler
  # 是否开启逻辑删除（orm框架本身不支持逻辑删除，可通过这种方式开启）
  logic_delete: true
  # 逻辑删除字段值（开启后默认为2）
  logic_delete_value: 2
  # 逻辑未删除字段（开启后默认为0）
  logic_not_delete_value: 0